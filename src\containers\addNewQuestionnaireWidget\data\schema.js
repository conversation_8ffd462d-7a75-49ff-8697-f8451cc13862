import { EMAIL, PHONE } from '@/utils/constants';
import * as yup from 'yup';
import strings from '@/utils/localization';

const getMessage = (key) => () => strings[key];

export const schema = yup.object({
  name: yup.string().required(getMessage('widgetNameRequired')).min(2, getMessage('minCharLength2')),
  dynamicWidget: yup.boolean(),
  questionnaire: yup.object().when('dynamicWidget', {
    is: (value) => value !== true,
    then: () =>
      yup
        .object()
        .shape({
          shortName: yup.string().required(getMessage('questionnaireRequired')),
        })
        .required(getMessage('questionnaireRequired')),
  }),
  defaultLanguage: yup.string().required(getMessage('defaultLanguageRequired')),
  spinnerText: yup.string(),
  repository: yup.string().when('dynamicWidget', {
    is: false,
    then: () => yup.string().required(getMessage('repositoryRequired')),
  }),
  identification: yup.string().required(getMessage('identificationRequired')),
  isOtpVerificationChecked: yup.boolean(),
  showProgressBar: yup.boolean(),
  showProgressPercentage: yup.boolean(),
  isReportPageChecked: yup.boolean(),
  isMultipleIndividualChecked: yup.boolean(),
  isDemographicsEnabled: yup.boolean(),
  isIntroChecked: yup.boolean(),
  introHeading: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('headingRequired')).min(2, getMessage('minCharLength2')),
  }),
  introDescription: yup.string().when('isIntroChecked', {
    is: true,
    then: () => yup.string().required(getMessage('descriptionRequired')).min(2, getMessage('minCharLength2')),
  }),
  saveForLaterHeading: yup.string(),
  saveForLaterDescription: yup.string(),
  discardHeading: yup.string(),
  discardDescription: yup.string(),
  isActionChecked: yup.boolean(),
  headingDescriptionChecked: yup.boolean(),
  finalPageHeading: yup.string().when('headingDescriptionChecked', {
    is: true,
    then: () => yup.string().required(getMessage('headingRequired')).min(2, getMessage('minCharLength2')),
  }),
  finalPageDescription: yup.string().when('headingDescriptionChecked', {
    is: true,
    then: () => yup.string().required(getMessage('descriptionRequired')).min(2, getMessage('minCharLength2')),
  }),
  actionData: yup.array().when('isActionChecked', {
    is: true,
    then: () =>
      yup.array().of(
        yup.object().shape({
          scoreDefinitionName: yup.string().test('skip-first', getMessage('scoreNameRequired'), function (value) {
            const index = this.options.index;
            return index === 0 || !!value; // Skip validation if first item, otherwise require
          }),
          selectedScore: yup.string().test('skip-first', getMessage('valueRequired'), function (value) {
            const index = this.options.index;
            return index === 0 || !!value;
          }),
          scoreValue: yup.string().when('selectedScore', {
            is: (value) => value && value !== 'between',
            then: () => yup.string().required(getMessage('valueRequired')),
          }),
          scoreFrom: yup.string().when('selectedScore', {
            is: (value) => value && value === 'between',
            then: () => yup.string().required(getMessage('valueMinRequired')),
          }),
          scoreTo: yup.string().when('selectedScore', {
            is: (value) => value && value === 'between',
            then: () => yup.string().required(getMessage('valueMaxRequired')),
          }),
          selectedAction: yup.string().required(getMessage('actionRequired')),
          actionHeading: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('headingRequired')),
          }),
          actionDescription: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Page',
            then: () => yup.string().required(getMessage('descriptionRequired')),
          }),
          selectedService: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Call a Service in Background',
            then: () => yup.string().required(getMessage('servicesRequired')),
          }),
          redirectUrl: yup.string().when('selectedAction', {
            is: (value) => value && value === 'URL',
            then: () => yup.string().url(getMessage('validURL')).required(getMessage('redirectUrlRequired')),
          }),
          selectedTarget: yup.string().when('selectedAction', {
            is: (value) => value && (value === 'URL' || value === 'Widget'),
            then: () => yup.string().required(getMessage('targetRequired')),
          }),
          selectedWidgetType: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Widget',
            then: () => yup.string().required(getMessage('widgetTypeRequired')),
          }),
          selectedWidget: yup
            .object()
            .shape()
            .when('selectedAction', {
              is: (value) => value && value === 'Widget',
              then: () =>
                yup.object({
                  name: yup.string().required(getMessage('widgetRequired')),
                }),
            }),
          backgroundServiceEndpoint: yup.string().when('selectedAction', {
            is: (value) => value && value === 'Service',
            then: () => yup.string().url(getMessage('apiEndpointValid')).required(getMessage('apiEndpointRequired')),
          }),
        }),
      ),
  }),
});

export const fieldsValidation = yup.array().of(
  yup.object().shape({
    code: yup.string().required(),
  }),
);
